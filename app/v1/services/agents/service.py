# app/v1/services/agents/service.py

import asyncio
from typing import Dict, Any, List, Optional
from app.shared.database.models import UserTenantDB
from app.shared.utils.logger import setup_new_logging
from app.shared.config.agent_config import agent_config

from .types import <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ontex<PERSON>, Agent<PERSON>tatus
from .registry import agent_registry
from .orchestrator import OrchestratorAgent
from .document_search_agent import DocumentSearchAgent
from .memory.session_memory import SessionMemoryManager
from .memory.user_context import UserContextManager

logger = setup_new_logging(__name__)

class AgentService:
    """
    Main service class for the multi-agent system.
    
    Coordinates:
    - Agent registration and management
    - Query processing through orchestrator
    - Memory management
    - Health monitoring
    """
    
    def __init__(self):
        self.session_manager = SessionMemoryManager()
        self.context_manager = UserContextManager()
        self._initialized = False
        
        # Initialize agents if not already done
        if not self._initialized:
            self._initialize_agents()
    
    def _initialize_agents(self):
        """Initialize and register all available agents."""
        try:
            # Register document search agent
            doc_agent = DocumentSearchAgent()
            agent_registry.register_agent(doc_agent)
            
            # Register orchestrator agent
            orchestrator = OrchestratorAgent()
            agent_registry.register_agent(orchestrator)
            
            self._initialized = True
            logger.info("Agent system initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize agents: {e}")
            raise
    
    async def process_query(
        self,
        context: AgentContext,
        current_user: UserTenantDB
    ) -> AgentResponse:
        """
        Process a query through the orchestrator.
        
        Args:
            context: The context containing user query and metadata
            current_user: Current user information
            
        Returns:
            AgentResponse: Response from the appropriate agent
        """
        try:
            # Get or create session
            if not context.session_id:
                context.session_id = self.session_manager.create_session(
                    user_id=context.user_id,
                    tenant_id=context.tenant_id,
                    conversation_id=context.conversation_id
                )
            
            # Get orchestrator
            orchestrator = agent_registry.get_agent("orchestrator")
            if not orchestrator:
                raise Exception("Orchestrator agent not available")
            
            # Process query
            response = await orchestrator.run(context, current_user)
            
            # Update session memory
            await self._update_session_memory(context, response)
            
            # Update user context
            await self._update_user_context(context, response)
            
            return response
            
        except Exception as e:
            logger.error(f"Error processing query: {e}")
            return AgentResponse(
                agent_type="orchestrator",
                agent_name="system",
                status=AgentStatus.ERROR,
                content="माफ गर्नुहोस्, केही समस्या भयो। कृपया फेरि प्रयास गर्नुहोस्।",
                error_message=str(e)
            )
    
    async def run_specific_agent(
        self,
        agent_name: str,
        context: AgentContext,
        current_user: UserTenantDB
    ) -> AgentResponse:
        """
        Run a specific agent directly.
        
        Args:
            agent_name: Name of the agent to run
            context: The context containing user query and metadata
            current_user: Current user information
            
        Returns:
            AgentResponse: Response from the specified agent
        """
        try:
            agent = agent_registry.get_agent(agent_name)
            if not agent:
                raise Exception(f"Agent '{agent_name}' not found")
            
            # Get or create session
            if not context.session_id:
                context.session_id = self.session_manager.create_session(
                    user_id=context.user_id,
                    tenant_id=context.tenant_id,
                    conversation_id=context.conversation_id
                )
            
            # Run the agent
            response = await agent.run(context, current_user)
            
            # Update memory
            await self._update_session_memory(context, response)
            await self._update_user_context(context, response)
            
            return response
            
        except Exception as e:
            logger.error(f"Error running agent {agent_name}: {e}")
            return AgentResponse(
                agent_type="unknown",
                agent_name=agent_name,
                status=AgentStatus.ERROR,
                content="माफ गर्नुहोस्, केही समस्या भयो। कृपया फेरि प्रयास गर्नुहोस्।",
                error_message=str(e)
            )
    
    async def _update_session_memory(
        self,
        context: AgentContext,
        response: AgentResponse
    ):
        """Update session memory with the interaction."""
        try:
            if context.session_id:
                # Add user message
                user_message = {
                    "role": "user",
                    "content": context.user_query,
                    "timestamp": response.timestamp.isoformat()
                }
                self.session_manager.add_to_conversation_history(
                    context.session_id,
                    user_message
                )
                
                # Add agent response
                agent_message = {
                    "role": "assistant",
                    "content": response.content,
                    "agent_name": response.agent_name,
                    "agent_type": response.agent_type,
                    "status": response.status,
                    "sources_count": len(response.sources),
                    "timestamp": response.timestamp.isoformat()
                }
                self.session_manager.add_to_conversation_history(
                    context.session_id,
                    agent_message
                )
                
        except Exception as e:
            logger.error(f"Failed to update session memory: {e}")
    
    async def _update_user_context(
        self,
        context: AgentContext,
        response: AgentResponse
    ):
        """Update user context with learned information."""
        try:
            # Extract topics from the query (simple keyword extraction)
            topics = self._extract_topics(context.user_query)
            
            # Update user context
            self.context_manager.update_user_context(
                user_id=context.user_id,
                tenant_id=context.tenant_id,
                learned_topics=topics,
                language_preference=context.language
            )
            
        except Exception as e:
            logger.error(f"Failed to update user context: {e}")
    
    def _extract_topics(self, query: str) -> List[str]:
        """Extract topics from user query (simple implementation)."""
        # Simple keyword-based topic extraction
        legal_keywords = {
            "संविधान": "constitution",
            "कानून": "law",
            "ऐन": "act",
            "नियम": "regulation",
            "अदालत": "court",
            "न्यायालय": "judiciary",
            "मुद्दा": "case",
            "फैसला": "judgment",
            "अधिकार": "rights",
            "कर्तव्य": "duties"
        }
        
        topics = []
        query_lower = query.lower()
        
        for keyword, topic in legal_keywords.items():
            if keyword in query_lower:
                topics.append(topic)
        
        return topics
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform a comprehensive health check of the agent system."""
        try:
            # Get agent registry health
            registry_health = agent_registry.health_check()
            
            # Check memory systems
            memory_status = {
                "session_manager": "healthy",
                "context_manager": "healthy"
            }
            
            try:
                # Test session manager
                test_stats = self.session_manager.get_session_stats()
                memory_status["session_stats"] = test_stats
            except Exception as e:
                memory_status["session_manager"] = f"error: {e}"
            
            try:
                # Test context manager  
                test_context = self.context_manager.get_user_context("test", "test")
                memory_status["context_test"] = "passed"
            except Exception as e:
                memory_status["context_manager"] = f"error: {e}"
            
            # Overall system status
            system_status = "healthy"
            if any("error" in str(status) for status in memory_status.values()):
                system_status = "degraded"
            
            if registry_health["total_agents"] == 0:
                system_status = "unhealthy"
            
            return {
                "system_status": system_status,
                "total_agents": registry_health["total_agents"],
                "agents_by_type": registry_health["agents_by_type"],
                "agents": registry_health["agents"],
                "memory_status": memory_status,
                "config": {
                    "agent_mode_enabled": agent_config.AGENT_MODE_ENABLED,
                    "default_timeout": agent_config.AGENT_DEFAULT_TIMEOUT,
                    "max_retries": agent_config.AGENT_MAX_RETRIES
                }
            }
            
        except Exception as e:
            logger.error(f"Health check failed: {e}")
            return {
                "system_status": "error",
                "error": str(e),
                "total_agents": 0,
                "agents_by_type": {},
                "agents": {},
                "memory_status": {"error": str(e)}
            }
    
    def list_agents(self) -> List[Dict[str, str]]:
        """List all available agents."""
        return agent_registry.list_agents()
    
    def get_agent_info(self, agent_name: str) -> Optional[Dict[str, Any]]:
        """Get information about a specific agent."""
        return agent_registry.get_agent_info(agent_name)
    
    async def create_session(
        self,
        user_id: str,
        tenant_id: str,
        conversation_id: Optional[str] = None
    ) -> str:
        """Create a new session."""
        return self.session_manager.create_session(
            user_id=user_id,
            tenant_id=tenant_id,
            conversation_id=conversation_id
        )
    
    async def get_user_sessions(
        self,
        user_id: str,
        tenant_id: str,
        limit: int = 10,
        active_only: bool = True
    ) -> List[Dict[str, Any]]:
        """Get user sessions."""
        return self.session_manager.get_user_sessions(
            user_id=user_id,
            tenant_id=tenant_id,
            limit=limit,
            active_only=active_only
        )
    
    async def deactivate_session(self, session_id: str) -> bool:
        """Deactivate a session."""
        return self.session_manager.deactivate_session(session_id)
    
    async def get_user_context(self, user_id: str, tenant_id: str) -> Dict[str, Any]:
        """Get user context."""
        return self.context_manager.get_user_context(user_id, tenant_id)
    
    async def get_tenant_stats(self, tenant_id: str) -> Dict[str, Any]:
        """Get statistics for a tenant."""
        try:
            session_stats = self.session_manager.get_session_stats(tenant_id)
            context_stats = self.context_manager.get_tenant_stats(tenant_id)
            
            return {
                "sessions": session_stats,
                "user_contexts": context_stats,
                "agents": agent_registry.health_check()
            }
            
        except Exception as e:
            logger.error(f"Failed to get tenant stats: {e}")
            return {"error": str(e)}
