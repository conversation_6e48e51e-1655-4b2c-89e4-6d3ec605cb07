# app/v1/services/agents/document_search_agent.py

import time
from typing import List
from app.shared.database.models import UserTenantDB
from app.v1.services.knowledge_base.service import KnowledgeBaseService
from .base_agent import BaseAgent
from .types import AgentResponse, AgentContext, AgentType, AgentStatus, SourceReference

class DocumentSearchAgent(BaseAgent):
    """
    Simple document search agent - just search and return results.
    """

    def __init__(self):
        super().__init__(
            name="document_search",
            agent_type=AgentType.DOCUMENT_SEARCH,
            timeout_seconds=30,
            max_retries=1
        )
    
    async def _process_query(self, context: AgentContext, current_user: UserTenantDB) -> AgentResponse:
        """
        Simple document search - just search and return grouped results.
        """
        start_time = time.time()

        try:
            # Use KnowledgeBaseService directly for search
            kb_service = KnowledgeBaseService(current_user)

            # Get grouped search results
            self.logger.info(f"Searching for: {context.user_query}")
            search_results = await kb_service.search_documents(
                context.user_query,
                current_user,
                context.max_results or 5
            )
            self.logger.info(f"Search returned {len(search_results)} grouped sources")

            # Convert grouped search results to legacy sources format
            sources = []
            for source_group in search_results:
                source_name = source_group.get("source", "unknown.pdf")
                for sentence in source_group.get("sentences", []):
                    sources.append(SourceReference(
                        document_id=sentence.get("sent_id", ""),
                        filename=source_name,
                        page_number=sentence.get("page_number", 0),
                        chunk_id=sentence.get("sent_id", ""),
                        text_snippet=sentence.get("text", "")[:200] + "..." if len(sentence.get("text", "")) > 200 else sentence.get("text", ""),
                        confidence_score=sentence.get("score", 0.0)
                    ))

            processing_time = (time.time() - start_time) * 1000

            # Generate a simple response based on search results
            if sources:
                content = f"तपाईंको खोज \"{context.user_query}\" को लागि {len(sources)} वटा सम्बन्धित दस्तावेजहरू फेला परेका छन्।"
            else:
                content = f"माफ गर्नुहोस्, तपाईंको खोज \"{context.user_query}\" को लागि कुनै सम्बन्धित दस्तावेज फेला परेन।"

            return AgentResponse(
                agent_type=self.agent_type,
                agent_name=self.name,
                status=AgentStatus.SUCCESS,
                content=content,
                sources=sources,
                metadata={
                    "total_sources_found": len(sources),
                    "search_type": "vector_search"
                },
                processing_time_ms=processing_time
            )

        except Exception as e:
            self.logger.error(f"Error in document search agent: {e}")

            processing_time = (time.time() - start_time) * 1000
            return AgentResponse(
                agent_type=self.agent_type,
                agent_name=self.name,
                status=AgentStatus.ERROR,
                content="माफ गर्नुहोस्, दस्तावेज खोज्दा समस्या भयो। कृपया फेरि प्रयास गर्नुहोस्।",
                processing_time_ms=processing_time,
                error_message=str(e)
            )

    def get_capabilities(self) -> List[str]:
        """Get list of capabilities this agent provides."""
        return [
            "document_search",
            "vector_search"
        ]
