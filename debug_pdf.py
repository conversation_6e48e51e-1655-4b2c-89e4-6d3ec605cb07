#!/usr/bin/env python3
# debug_pdf.py - Debug PDF font detection

import fitz  # PyMuPDF
import sys

def debug_pdf_fonts(pdf_path):
    """Debug PDF to see what fonts are used."""
    try:
        print(f"Opening PDF: {pdf_path}")
        doc = fitz.open(pdf_path)
        print(f"PDF has {len(doc)} pages")
        
        all_fonts = set()
        
        for page_num in range(min(3, len(doc))):  # Check first 3 pages only
            print(f"\n--- Page {page_num + 1} ---")
            page = doc.load_page(page_num)
            
            # Get text blocks with font information
            blocks = page.get_text("dict")
            
            for block_num, block in enumerate(blocks["blocks"]):
                if "lines" in block:
                    for line_num, line in enumerate(block["lines"]):
                        for span_num, span in enumerate(line["spans"]):
                            text = span["text"].strip()
                            font = span.get("font", "")
                            size = span.get("size", 0)
                            
                            if text and font:  # Only show non-empty text
                                all_fonts.add(font)
                                print(f"  Font: '{font}' Size: {size:.1f} Text: '{text[:50]}...'")
        
        doc.close()
        
        print(f"\n--- Summary ---")
        print(f"Total unique fonts found: {len(all_fonts)}")
        for font in sorted(all_fonts):
            print(f"  - {font}")
        
        return all_fonts
        
    except Exception as e:
        print(f"Error: {e}")
        return set()

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python debug_pdf.py <pdf_file>")
        sys.exit(1)
    
    pdf_file = sys.argv[1]
    debug_pdf_fonts(pdf_file)
