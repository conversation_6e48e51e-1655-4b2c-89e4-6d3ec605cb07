import argparse
import os
import fitz  # PyMuPDF
from pathlib import Path
import re

unicodeatoz=["ब","द","अ","म","भ","ा","न","ज","ष्","व","प","ि","फ","ल","य","उ","त्र","च","क","त","ग","ख","ध","ह","थ","श"]
unicodeAtoZ=["ब्","ध","ऋ","म्","भ्","ँ","न्","ज्","क्ष्","व्","प्","ी","ः","ल्","इ","ए","त्त","च्","क्","त्","ग्","ख्","ध्","ह्","थ्","श्"]
unicode0to9=["ण्","ज्ञ","द्द","घ","द्ध","छ","ट","ठ","ड","ढ"]
symbolsDict=\
{
    "~":"ञ्",
    "`":"ञ",
    "!":"१",
    "@":"२",
    "#":"३",
    "$":"४",
    "%":"५",
    "^":"६",
    "&":"७",
    "*":"८",
    "(":"९",
    ")":"०",
    "-":"(",
    "_":")",
    "+":"ं",
    "[":"ृ",
    "{":"र्",
    "]":"े",
    "}":"ै",
    "\\":"्",
    "|":"्र",
    ";":"स",
    ":":"स्",
    "'":"ु",
    "\"":"ू",
    ",":",",
    "<":"?",
    ".":"।",
    ">":"श्र",
    "/":"र",
    "?":"रु",
    "=":".",
    "ˆ":"फ्",
    "Î":"ङ्ख",
    "å":"द्व",
    "÷":"/"
}


def normalizePreeti(preetitxt):
    normalized=''
    previoussymbol=''
    preetitxt=preetitxt.replace('qm','s|')
    preetitxt=preetitxt.replace('f]','ो')
    preetitxt=preetitxt.replace('km','फ')
    preetitxt=preetitxt.replace('0f','ण')
    preetitxt=preetitxt.replace('If','क्ष')
    preetitxt=preetitxt.replace('if','ष')
    preetitxt=preetitxt.replace('cf','आ')
    index=-1
    while index+1 < len(preetitxt):
        index+=1
        character=preetitxt[index]
        try:
            if preetitxt[index+2] == '{':
                if preetitxt[index+1] == 'f' or preetitxt[index+1] == 'ो':
                    normalized+='{'+character+preetitxt[index+1]
                    index+=2
                    continue
            if preetitxt[index+1] == '{':
                if character!='f':
                    normalized+='{'+character
                    index+=1
                    continue
        except IndexError:
            pass
        if character=='l':
            previoussymbol='l'
            continue
        else:
            normalized+=character+previoussymbol
            previoussymbol=''
    return normalized

def is_preeti_font(font_name):
    """Check if a font is Preeti or similar Nepali font."""
    if not font_name:
        return False

    font_name = font_name.lower()
    preeti_indicators = [
        'preeti', 'pcs', 'nepali', 'devanagari', 'kantipur', 'sagarmatha',
        'fontasy', 'kanchan', 'walkman', 'ananda', 'everest'
    ]

    return any(indicator in font_name for indicator in preeti_indicators)

def extract_text_with_fonts_from_pdf(pdf_path):
    """Extract text from PDF with font information."""
    try:
        doc = fitz.open(pdf_path)
        all_text = ""
        preeti_text = ""

        print(f"Processing PDF with {len(doc)} pages...")

        for page_num in range(len(doc)):
            page = doc.load_page(page_num)

            # Get text blocks with font information
            blocks = page.get_text("dict")

            page_text = ""
            page_preeti_text = ""

            for block in blocks["blocks"]:
                if "lines" in block:
                    for line in block["lines"]:
                        for span in line["spans"]:
                            text = span["text"]
                            font = span.get("font", "")

                            page_text += text

                            # Check if this text uses Preeti font
                            if is_preeti_font(font):
                                page_preeti_text += text
                                print(f"Found Preeti text in font '{font}': {text[:50]}...")
                            else:
                                # For debugging - show what fonts are being used
                                if text.strip() and font:
                                    print(f"Non-Preeti font '{font}': {text[:30]}...")

            all_text += page_text + "\n"
            preeti_text += page_preeti_text + "\n"

        doc.close()

        print(f"\nExtracted {len(all_text)} characters total")
        print(f"Identified {len(preeti_text)} characters as Preeti font")

        # If no Preeti text found, try to detect it by content
        if not preeti_text.strip():
            print("\nNo Preeti font text detected in the PDF.")
            print("Analyzing content to detect Preeti text patterns...")

            # Try to detect Preeti text by analyzing character patterns
            potential_preeti = ""
            for line in all_text.split('\n'):
                if is_likely_preeti_text(line):
                    potential_preeti += line + '\n'

            if potential_preeti.strip():
                print(f"Found {len(potential_preeti)} characters that appear to be Preeti text")
                return potential_preeti
            else:
                print("No Preeti text patterns detected. Converting all text.")
                return all_text

        return preeti_text

    except Exception as e:
        print(f"Error extracting text from PDF: {e}")
        return None

def extract_text_from_pdf(pdf_path):
    """Extract text from PDF file - wrapper for backward compatibility."""
    return extract_text_with_fonts_from_pdf(pdf_path)

def read_input_file(inputfile):
    """Read input file - handle both text and PDF files."""
    file_extension = Path(inputfile).suffix.lower()

    if file_extension == '.pdf':
        print("Detected PDF file. Extracting text...")
        text = extract_text_from_pdf(inputfile)
        if text is None:
            raise ValueError("Failed to extract text from PDF")
        return text
    else:
        # Try to read as text file with different encodings
        encodings = ['utf-8', 'utf-16', 'latin-1', 'cp1252']
        for encoding in encodings:
            try:
                with open(inputfile, "r", encoding=encoding) as fp:
                    return fp.read()
            except UnicodeDecodeError:
                continue
        raise ValueError(f"Could not read file {inputfile} with any supported encoding")

def is_likely_preeti_text(text):
    """Check if text is likely to be in Preeti encoding based on character patterns."""
    if not text.strip():
        return False

    # Common Preeti character patterns and combinations
    preeti_indicators = [
        # Common Preeti characters and combinations
        'f', 'g', 'h', 'j', 'k', 'l', 'm', 'n', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z',
        # Common Preeti punctuation and special chars
        '{', '}', '[', ']', '|', '\\', ';', ':', "'", '"', '~', '`',
        # Common Preeti combinations
        'sf', 'df', 'gf', 'tf', 'bf', 'kf', 'rf', 'nf', 'xf',
        'f]', 'f{', 'f}', 'f\\',
        ';+', ':+', 'L+', 'g+', 'k+', 'r+',
    ]

    # Check for Preeti-specific patterns
    preeti_patterns = [
        r'[a-z]+f[a-z]*',     # Common Preeti pattern with 'f'
        r'[a-z]*[{}[\]|\\]',  # Special characters common in Preeti
        r'[a-z]+[;:]+',       # Letters followed by Preeti punctuation
        r'[a-z]+\+',          # Plus signs in Preeti
        r'[a-z]*f\]',         # f] pattern very common in Preeti
    ]

    pattern_matches = 0
    for pattern in preeti_patterns:
        if re.search(pattern, text):
            pattern_matches += 1

    # Count Preeti indicator characters
    indicator_count = 0
    for indicator in preeti_indicators:
        if indicator in text:
            indicator_count += 1

    # Check for high ratio of ASCII characters (common in Preeti)
    ascii_chars = sum(1 for c in text if ord(c) < 128)
    total_chars = len(text.strip())
    ascii_ratio = ascii_chars / total_chars if total_chars > 0 else 0

    # Decision logic
    if pattern_matches >= 2:  # Multiple Preeti patterns found
        return True
    elif indicator_count >= 3 and ascii_ratio > 0.8:  # Many Preeti chars + high ASCII
        return True
    elif 'f]' in text or 'sf' in text or 'gf' in text:  # Very common Preeti combinations
        return True

    return False

def convert_text_intelligently(text):
    """Convert text, handling both Preeti and Unicode content."""
    lines = text.split('\n')
    converted_lines = []

    for line in lines:
        if not line.strip():
            converted_lines.append(line)
            continue

        # Check if this line looks like Preeti text
        if is_likely_preeti_text(line):
            print(f"Converting Preeti line: {line[:50]}...")
            converted_line = convert_preeti_line(line)
            converted_lines.append(converted_line)
        else:
            # Keep Unicode text as-is
            print(f"Keeping Unicode line: {line[:50]}...")
            converted_lines.append(line)

    return '\n'.join(converted_lines)

def convert_preeti_line(preeti_text):
    """Convert a single line of Preeti text to Unicode."""
    converted = ''
    normalized_preeti = normalizePreeti(preeti_text)

    for character in normalized_preeti:
        try:
            if ord(character) >= 97 and ord(character) <= 122:
                converted += unicodeatoz[ord(character)-97]
            elif ord(character) >= 65 and ord(character) <= 90:
                converted += unicodeAtoZ[ord(character)-65]
            elif ord(character) >= 48 and ord(character) <= 57:
                converted += unicode0to9[ord(character)-48]
            else:
                converted += symbolsDict[character]
        except (KeyError, IndexError):
            converted += character

    return converted

def convert(inputfile):
    preeti = read_input_file(inputfile)

    if preeti is None:
        return "Error: Could not read input file"

    # Use intelligent conversion that handles mixed content
    converted = convert_text_intelligently(preeti)

    return converted

argparser=argparse.ArgumentParser(description='Convert Preeti text to Unicode')
argparser.add_argument('inputFile',help='Input file name with Preeti text')
argparser.add_argument('outputFile',help='Output file name for Unicode text')
args=argparser.parse_args()

if os.path.exists(args.inputFile) and os.path.isfile(args.inputFile):
    if not os.path.exists(args.outputFile):
        with open(args.outputFile,"w",encoding='utf-8') as fp:
            fp.write(convert(args.inputFile))
            print("Output saved to {}".format(args.outputFile))
    else:
        print("File {} already exists!\nAborting to avoid overwriting.".format(args.outputFile))
else:
    print("File {} doesn't exist!".format(args.inputFile))