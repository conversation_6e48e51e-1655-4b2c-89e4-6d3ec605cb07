#!/usr/bin/env python3
# test_new_architecture.py

"""
Simple test script to verify the new LLM-powered main agent architecture.
This script tests the core functionality without requiring a full server setup.
"""

import asyncio
import sys
import os
from unittest.mock import Mock, patch

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

async def test_main_agent_initialization():
    """Test that the MainAgent can be initialized."""
    print("Testing MainAgent initialization...")
    
    try:
        with patch('app.v1.services.agents.main_agent.ChatOpenAI'), \
             patch('app.v1.services.agents.main_agent.MongoMemoryStore'), \
             patch('app.v1.services.agents.main_agent.MongoDBCheckpointer'):
            
            from app.v1.services.agents.main_agent import MainAgent
            
            agent = MainAgent()
            
            assert agent.name == "main_agent"
            assert agent.agent_type.value == "main_agent"
            assert agent.tool_selector is not None
            assert agent.synthesis_engine is not None
            assert agent.context_manager is not None
            
            print("✓ MainAgent initialization successful")
            return True
            
    except Exception as e:
        print(f"✗ MainAgent initialization failed: {e}")
        return False

async def test_tool_selector():
    """Test the ToolSelector functionality."""
    print("Testing ToolSelector...")
    
    try:
        with patch('app.v1.services.agents.tool_selection.ChatOpenAI'):
            from app.v1.services.agents.tool_selection import ToolSelector
            
            selector = ToolSelector()
            
            # Test available tools
            assert "search_documents" in selector.available_tools
            assert "general_chat" in selector.available_tools
            assert "analyze_legal_content" in selector.available_tools
            
            # Test fallback selection
            decision = selector._get_fallback_tool_selection("search for legal documents")
            assert "search_documents" in decision.selected_tools
            
            print("✓ ToolSelector functionality working")
            return True
            
    except Exception as e:
        print(f"✗ ToolSelector test failed: {e}")
        return False

async def test_response_synthesis():
    """Test the ResponseSynthesisEngine."""
    print("Testing ResponseSynthesisEngine...")
    
    try:
        with patch('app.v1.services.agents.response_synthesis.ChatOpenAI'):
            from app.v1.services.agents.response_synthesis import ResponseSynthesisEngine
            from app.v1.services.agents.types import SourceReference
            
            engine = ResponseSynthesisEngine()
            
            # Test source grouping
            tool_results = [{
                "tool_name": "search_documents",
                "sources": [
                    SourceReference(
                        document_id="doc1",
                        filename="test.pdf",
                        page_number=1,
                        text_snippet="Test content"
                    )
                ]
            }]
            
            grouped = engine._group_sources_by_document(tool_results)
            assert "test.pdf" in grouped
            assert len(grouped["test.pdf"]) == 1
            
            print("✓ ResponseSynthesisEngine functionality working")
            return True
            
    except Exception as e:
        print(f"✗ ResponseSynthesisEngine test failed: {e}")
        return False

async def test_context_manager():
    """Test the ContextManager."""
    print("Testing ContextManager...")
    
    try:
        with patch('app.v1.services.agents.memory.context_manager.MongoMemoryStore'):
            from app.v1.services.agents.memory.context_manager import ContextManager
            
            manager = ContextManager()
            
            assert manager.max_conversation_length == 50
            assert manager.context_window_size == 10
            
            print("✓ ContextManager functionality working")
            return True
            
    except Exception as e:
        print(f"✗ ContextManager test failed: {e}")
        return False

async def test_agent_service():
    """Test the AgentService with new architecture."""
    print("Testing AgentService...")
    
    try:
        with patch('app.v1.services.agents.main_agent.ChatOpenAI'), \
             patch('app.v1.services.agents.main_agent.MongoMemoryStore'), \
             patch('app.v1.services.agents.main_agent.MongoDBCheckpointer'), \
             patch('app.v1.services.agents.document_search_agent.KnowledgeBaseService'):
            
            from app.v1.services.agents.service import AgentService
            
            service = AgentService()
            
            # Test that main agent is registered
            main_agent = service.agent_registry.get_agent("main_agent")
            assert main_agent is not None
            assert main_agent.name == "main_agent"
            
            # Test agent listing
            agents = service.list_agents()
            agent_names = [agent.get("name") for agent in agents]
            assert "main_agent" in agent_names
            assert "document_search" in agent_names
            
            print("✓ AgentService functionality working")
            return True
            
    except Exception as e:
        print(f"✗ AgentService test failed: {e}")
        return False

async def test_agent_types():
    """Test that new agent types are properly defined."""
    print("Testing agent types...")
    
    try:
        from app.v1.services.agents.types import AgentType
        
        # Test that new agent types exist
        assert hasattr(AgentType, 'MAIN_AGENT')
        assert AgentType.MAIN_AGENT == "main_agent"
        assert hasattr(AgentType, 'ANALYSIS')
        assert hasattr(AgentType, 'DATA_RETRIEVAL')
        
        print("✓ Agent types properly defined")
        return True
        
    except Exception as e:
        print(f"✗ Agent types test failed: {e}")
        return False

async def run_all_tests():
    """Run all tests and report results."""
    print("=" * 60)
    print("Testing New LLM-Powered Agent Architecture")
    print("=" * 60)
    
    tests = [
        test_agent_types,
        test_main_agent_initialization,
        test_tool_selector,
        test_response_synthesis,
        test_context_manager,
        test_agent_service
    ]
    
    results = []
    for test in tests:
        try:
            result = await test()
            results.append(result)
        except Exception as e:
            print(f"✗ Test {test.__name__} crashed: {e}")
            results.append(False)
        print()
    
    print("=" * 60)
    print("Test Results Summary")
    print("=" * 60)
    
    passed = sum(results)
    total = len(results)
    
    print(f"Passed: {passed}/{total}")
    print(f"Failed: {total - passed}/{total}")
    
    if passed == total:
        print("🎉 All tests passed! New architecture is working correctly.")
        return True
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return False

if __name__ == "__main__":
    success = asyncio.run(run_all_tests())
    sys.exit(0 if success else 1)
