#!/usr/bin/env python3
# simple_test.py - Simple test of the conversion

import sys
import fitz

def simple_convert():
    pdf_path = "/home/<USER>/Downloads/Payment-System-related-Unified-Directives-2081-1.pdf"
    
    print("Opening PDF...")
    doc = fitz.open(pdf_path)
    print(f"PDF has {len(doc)} pages")
    
    # Extract only Preeti text from first page
    page = doc.load_page(1)  # Page 2 (0-indexed)
    blocks = page.get_text("dict")
    
    preeti_text = ""
    for block in blocks["blocks"]:
        if "lines" in block:
            for line in block["lines"]:
                for span in line["spans"]:
                    text = span["text"]
                    font = span.get("font", "")
                    
                    if font == "Preeti":
                        preeti_text += text + " "
    
    doc.close()
    
    print(f"Extracted Preeti text: {preeti_text[:200]}...")
    
    # Simple conversion test
    converted = ""
    for char in preeti_text[:100]:  # Convert first 100 chars only
        if char == 'g':
            converted += 'न'
        elif char == ']':
            converted += 'े'
        elif char == 'k':
            converted += 'प'
        elif char == 'f':
            converted += 'ा'
        elif char == 'n':
            converted += 'ल'
        else:
            converted += char
    
    print(f"Converted text: {converted}")
    
    # Save to file
    with open("simple_output.txt", "w", encoding="utf-8") as f:
        f.write(converted)
    
    print("Saved to simple_output.txt")

if __name__ == "__main__":
    simple_convert()
