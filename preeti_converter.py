#!/usr/bin/env python3
# preeti_converter.py - Improved Preeti to Unicode converter with proper font detection

import argparse
import os
import fitz  # PyMuPDF
from pathlib import Path
import re

# Preeti to Unicode mapping tables
unicodeatoz=["ब","द","अ","म","भ","ा","न","ज","ष्","व","प","ि","फ","ल","य","उ","त्र","च","क","त","ग","ख","ध","ह","थ","श"]
unicodeAtoZ=["ब्","ध","ऋ","म्","भ्","ँ","न्","ज्","क्ष्","व्","प्","ी","ः","ल्","इ","ए","त्त","च्","क्","त्","ग्","ख्","ध्","ह्","थ्","श्"]
unicode0to9=["ण्","ज्ञ","द्द","घ","द्ध","छ","ट","ठ","ड","ढ"]
symbolsDict={
    "~":"ञ्", "`":"ञ", "!":"१", "@":"२", "#":"३", "$":"४", "%":"५", "^":"६", "&":"७", "*":"८", "(":"९", ")":"०",
    "-":"(", "_":")", "+":"ं", "[":"ृ", "{":"र्", "]":"े", "}":"ै", "\\":"्", "|":"्र", ";":"स", ":":"स्",
    "'":"ु", "\"":"ू", ",":"," , "<":"?", ".":"।", ">":"श्र", "/":"र", "?":"रु", "=":".", "ˆ":"फ्",
    "Î":"ङ्ख", "å":"द्व", "÷":"/"
}

def is_preeti_font(font_name):
    """Check if a font is Preeti or similar Nepali font."""
    if not font_name:
        return False
    
    font_name = font_name.lower()
    preeti_indicators = [
        'preeti', 'pcs', 'nepali', 'devanagari', 'kantipur', 'sagarmatha',
        'fontasy', 'kanchan', 'walkman', 'ananda', 'everest'
    ]
    
    return any(indicator in font_name for indicator in preeti_indicators)

def normalizePreeti(preetitxt):
    """Normalize Preeti text before conversion."""
    normalized = ''
    previoussymbol = ''
    preetitxt = preetitxt.replace('qm','s|')
    preetitxt = preetitxt.replace('f]','ो')
    preetitxt = preetitxt.replace('km','फ')
    preetitxt = preetitxt.replace('0f','ण')
    preetitxt = preetitxt.replace('If','क्ष')
    preetitxt = preetitxt.replace('if','ष')
    preetitxt = preetitxt.replace('cf','आ')
    
    index = -1
    while index + 1 < len(preetitxt):
        index += 1
        character = preetitxt[index]
        try:
            if preetitxt[index+2] == '{':
                if preetitxt[index+1] == 'f' or preetitxt[index+1] == 'ो':
                    normalized += '{' + character + preetitxt[index+1]
                    index += 2
                    continue
            if preetitxt[index+1] == '{':
                if character != 'f':
                    normalized += '{' + character
                    index += 1
                    continue
        except IndexError:
            pass
        
        if character == 'l':
            previoussymbol = 'l'
            continue
        else:
            normalized += character + previoussymbol
            previoussymbol = ''
    
    return normalized

def convert_preeti_to_unicode(preeti_text):
    """Convert Preeti text to Unicode."""
    converted = ''
    normalized_preeti = normalizePreeti(preeti_text)
    
    for character in normalized_preeti:
        try:
            if ord(character) >= 97 and ord(character) <= 122:  # a-z
                converted += unicodeatoz[ord(character)-97]
            elif ord(character) >= 65 and ord(character) <= 90:  # A-Z
                converted += unicodeAtoZ[ord(character)-65]
            elif ord(character) >= 48 and ord(character) <= 57:  # 0-9
                converted += unicode0to9[ord(character)-48]
            else:
                converted += symbolsDict.get(character, character)
        except (KeyError, IndexError):
            converted += character
    
    return converted

def extract_text_with_font_detection(pdf_path):
    """Extract text from PDF with proper font detection."""
    try:
        print(f"Processing PDF: {pdf_path}")
        doc = fitz.open(pdf_path)
        print(f"PDF has {len(doc)} pages")
        
        all_text = ""
        preeti_text = ""
        font_stats = {}
        
        for page_num in range(len(doc)):
            print(f"Processing page {page_num + 1}...")
            page = doc.load_page(page_num)
            
            # Get text blocks with font information
            blocks = page.get_text("dict")
            
            for block in blocks["blocks"]:
                if "lines" in block:
                    for line in block["lines"]:
                        for span in line["spans"]:
                            text = span["text"]
                            font = span.get("font", "")
                            
                            if text.strip():  # Only process non-empty text
                                all_text += text
                                
                                # Track font usage
                                font_stats[font] = font_stats.get(font, 0) + len(text)
                                
                                # Check if this text uses Preeti font
                                if is_preeti_font(font):
                                    preeti_text += text
        
        doc.close()
        
        print(f"\nFont Statistics:")
        for font, char_count in sorted(font_stats.items(), key=lambda x: x[1], reverse=True):
            print(f"  {font}: {char_count} characters")
        
        print(f"\nExtracted {len(all_text)} characters total")
        print(f"Identified {len(preeti_text)} characters as Preeti font")
        
        # If we found Preeti text, use it; otherwise use all text
        if preeti_text.strip():
            return preeti_text
        else:
            print("No Preeti font detected. Using all text for conversion.")
            return all_text
        
    except Exception as e:
        print(f"Error extracting text from PDF: {e}")
        return None

def read_input_file(inputfile):
    """Read input file - handle both text and PDF files."""
    file_extension = Path(inputfile).suffix.lower()
    
    if file_extension == '.pdf':
        print("Detected PDF file. Extracting text with font detection...")
        return extract_text_with_font_detection(inputfile)
    else:
        # Try to read as text file with different encodings
        encodings = ['utf-8', 'utf-16', 'latin-1', 'cp1252']
        for encoding in encodings:
            try:
                with open(inputfile, "r", encoding=encoding) as fp:
                    return fp.read()
            except UnicodeDecodeError:
                continue
        raise ValueError(f"Could not read file {inputfile} with any supported encoding")

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description='Convert Preeti text to Unicode with proper font detection')
    parser.add_argument('inputFile', help='Input file name (PDF or text file with Preeti text)')
    parser.add_argument('outputFile', help='Output file name for Unicode text')
    parser.add_argument('--debug', action='store_true', help='Enable debug output')
    args = parser.parse_args()

    if not os.path.exists(args.inputFile) or not os.path.isfile(args.inputFile):
        print(f"Error: File {args.inputFile} doesn't exist!")
        return 1

    if os.path.exists(args.outputFile):
        print(f"Error: File {args.outputFile} already exists! Aborting to avoid overwriting.")
        return 1

    try:
        # Read input file
        preeti_text = read_input_file(args.inputFile)
        
        if preeti_text is None:
            print("Error: Could not read input file")
            return 1
        
        if args.debug:
            print(f"Input text (first 200 chars): {preeti_text[:200]}...")
        
        # Convert to Unicode
        print("Converting Preeti text to Unicode...")
        unicode_text = convert_preeti_to_unicode(preeti_text)
        
        if args.debug:
            print(f"Output text (first 200 chars): {unicode_text[:200]}...")
        
        # Save output
        with open(args.outputFile, "w", encoding='utf-8') as fp:
            fp.write(unicode_text)
        
        print(f"Conversion completed! Output saved to {args.outputFile}")
        print(f"Converted {len(preeti_text)} input characters to {len(unicode_text)} output characters")
        
        return 0
        
    except Exception as e:
        print(f"Error during conversion: {e}")
        return 1

if __name__ == "__main__":
    exit(main())
