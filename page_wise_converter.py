#!/usr/bin/env python3
# page_wise_converter.py - Convert PDF page by page and save each page as separate PDF

import argparse
import os
import fitz  # PyMuPDF
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import A4
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, PageBreak
from reportlab.lib.units import inch
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.lib.enums import TA_LEFT
import textwrap
import urllib.request
import os

# Preeti to Unicode mapping
unicodeatoz=["ब","द","अ","म","भ","ा","न","ज","ष्","व","प","ि","फ","ल","य","उ","त्र","च","क","त","ग","ख","ध","ह","थ","श"]
unicodeAtoZ=["ब्","ध","ऋ","म्","भ्","ँ","न्","ज्","क्ष्","व्","प्","ी","ः","ल्","इ","ए","त्त","च्","क्","त्","ग्","ख्","ध्","ह्","थ्","श्"]
unicode0to9=["ण्","ज्ञ","द्द","घ","द्ध","छ","ट","ठ","ड","ढ"]
symbolsDict={
    "~":"ञ्", "`":"ञ", "!":"१", "@":"२", "#":"३", "$":"४", "%":"५", "^":"६", "&":"७", "*":"८", "(":"९", ")":"०",
    "-":"(", "_":")", "+":"ं", "[":"ृ", "{":"र्", "]":"े", "}":"ै", "\\":"्", "|":"्र", ";":"स", ":":"स्",
    "'":"ु", "\"":"ू", ",":"," , "<":"?", ".":"।", ">":"श्र", "/":"र", "?":"रु", "=":".", "ˆ":"फ्",
    "Î":"ङ्ख", "å":"द्व", "÷":"/"
}

def is_preeti_font(font_name):
    if not font_name:
        return False
    font_name = font_name.lower()
    return any(indicator in font_name for indicator in ['preeti', 'pcs', 'nepali'])

def normalizePreeti(preetitxt):
    normalized = ''
    previoussymbol = ''
    preetitxt = preetitxt.replace('qm','s|').replace('f]','ो').replace('km','फ')
    preetitxt = preetitxt.replace('0f','ण').replace('If','क्ष').replace('if','ष').replace('cf','आ')
    
    index = -1
    while index + 1 < len(preetitxt):
        index += 1
        character = preetitxt[index]
        try:
            if preetitxt[index+2] == '{':
                if preetitxt[index+1] == 'f' or preetitxt[index+1] == 'ो':
                    normalized += '{' + character + preetitxt[index+1]
                    index += 2
                    continue
            if preetitxt[index+1] == '{':
                if character != 'f':
                    normalized += '{' + character
                    index += 1
                    continue
        except IndexError:
            pass
        
        if character == 'l':
            previoussymbol = 'l'
            continue
        else:
            normalized += character + previoussymbol
            previoussymbol = ''
    
    return normalized

def convert_preeti_to_unicode(preeti_text):
    converted = ''
    normalized_preeti = normalizePreeti(preeti_text)
    
    for character in normalized_preeti:
        try:
            if ord(character) >= 97 and ord(character) <= 122:  # a-z
                converted += unicodeatoz[ord(character)-97]
            elif ord(character) >= 65 and ord(character) <= 90:  # A-Z
                converted += unicodeAtoZ[ord(character)-65]
            elif ord(character) >= 48 and ord(character) <= 57:  # 0-9
                converted += unicode0to9[ord(character)-48]
            else:
                converted += symbolsDict.get(character, character)
        except (KeyError, IndexError):
            converted += character
    
    return converted

def process_page(page, page_num):
    print(f"  Processing page {page_num + 1}...")
    blocks = page.get_text("dict")
    page_content = []
    
    for block in blocks["blocks"]:
        if "lines" in block:
            block_text = ""
            for line in block["lines"]:
                line_text = ""
                for span in line["spans"]:
                    text = span["text"]
                    font = span.get("font", "")
                    
                    if text.strip():
                        if is_preeti_font(font):
                            converted_text = convert_preeti_to_unicode(text)
                            line_text += converted_text
                        else:
                            line_text += text
                
                if line_text.strip():
                    block_text += line_text + "\n"
            
            if block_text.strip():
                page_content.append(block_text.strip())
    
    return "\n\n".join(page_content)

def setup_unicode_font():
    """Setup Unicode font for proper Devanagari rendering."""
    try:
        # Try to use system fonts that support Devanagari
        font_paths = [
            '/usr/share/fonts/truetype/noto/NotoSansDevanagari-Regular.ttf',
            '/usr/share/fonts/truetype/liberation/LiberationSans-Regular.ttf',
            '/System/Library/Fonts/Arial.ttf',  # macOS
            'C:/Windows/Fonts/arial.ttf',  # Windows
        ]

        for font_path in font_paths:
            if os.path.exists(font_path):
                pdfmetrics.registerFont(TTFont('DevanagariFont', font_path))
                return 'DevanagariFont'

        # Fallback to Helvetica
        return 'Helvetica'

    except Exception as e:
        print(f"Warning: Could not setup Unicode font: {e}")
        return 'Helvetica'

def create_single_pdf_from_pages(converted_pages, output_pdf):
    """Create a single PDF from all converted pages."""
    try:
        print(f"Creating single PDF: {output_pdf}")

        # Setup Unicode font
        font_name = setup_unicode_font()

        # Create PDF document
        doc = SimpleDocTemplate(output_pdf, pagesize=A4)
        story = []

        # Get styles
        styles = getSampleStyleSheet()

        # Create custom styles with Unicode font
        heading_style = ParagraphStyle(
            'CustomHeading',
            parent=styles['Heading1'],
            fontName=font_name,
            fontSize=16,
            spaceAfter=12,
            alignment=TA_LEFT
        )

        normal_style = ParagraphStyle(
            'CustomNormal',
            parent=styles['Normal'],
            fontName=font_name,
            fontSize=12,
            spaceAfter=6,
            alignment=TA_LEFT
        )

        for page_num, page_content in enumerate(converted_pages):
            # Add page header
            page_header = f"Page {page_num + 1}"
            story.append(Paragraph(page_header, heading_style))
            story.append(Spacer(1, 0.2*inch))

            # Add page content
            if page_content.strip():
                # Split content into paragraphs
                paragraphs = page_content.split('\n\n')

                for para in paragraphs:
                    if para.strip():
                        # Clean and format text
                        clean_para = para.replace('\n', ' ').strip()
                        if len(clean_para) > 100:
                            # Break long paragraphs
                            wrapped_lines = textwrap.wrap(clean_para, width=80)
                            for line in wrapped_lines:
                                story.append(Paragraph(line, normal_style))
                        else:
                            story.append(Paragraph(clean_para, normal_style))

                        story.append(Spacer(1, 0.1*inch))
            else:
                # Empty page
                story.append(Paragraph("(Empty page)", normal_style))

            # Add page break except for last page
            if page_num < len(converted_pages) - 1:
                story.append(PageBreak())

        # Build PDF
        doc.build(story)
        print(f"Single PDF created successfully: {output_pdf}")
        return True

    except Exception as e:
        print(f"Error creating single PDF: {e}")
        return False

def create_pdf_from_text(text_content, output_pdf, page_number):
    """Create a PDF from converted text content (for individual pages)."""
    try:
        # Setup Unicode font
        font_name = setup_unicode_font()

        # Create PDF document
        doc = SimpleDocTemplate(output_pdf, pagesize=A4)
        story = []

        # Get styles
        styles = getSampleStyleSheet()

        # Create custom styles with Unicode font
        heading_style = ParagraphStyle(
            'CustomHeading',
            parent=styles['Heading1'],
            fontName=font_name,
            fontSize=16,
            spaceAfter=12
        )

        normal_style = ParagraphStyle(
            'CustomNormal',
            parent=styles['Normal'],
            fontName=font_name,
            fontSize=12,
            spaceAfter=6
        )

        # Add page header
        page_header = f"Page {page_number}"
        story.append(Paragraph(page_header, heading_style))
        story.append(Spacer(1, 0.2*inch))

        # Split content into paragraphs
        if text_content.strip():
            paragraphs = text_content.split('\n\n')

            for para in paragraphs:
                if para.strip():
                    clean_para = para.replace('\n', ' ').strip()
                    story.append(Paragraph(clean_para, normal_style))
                    story.append(Spacer(1, 0.1*inch))
        else:
            # Empty page
            story.append(Paragraph("(Empty page)", normal_style))

        # Build PDF
        doc.build(story)
        return True

    except Exception as e:
        print(f"    Error creating PDF for page {page_number}: {e}")
        return False

def convert_pdf_page_wise(pdf_path, output_base="converted", output_mode="single"):
    try:
        print(f"Processing PDF: {pdf_path}")
        doc = fitz.open(pdf_path)
        print(f"PDF has {len(doc)} pages")

        converted_pages = []

        # Process all pages first
        for page_num in range(len(doc)):
            page = doc.load_page(page_num)
            converted_text = process_page(page, page_num)
            converted_pages.append(converted_text)

        doc.close()

        success_count = 0

        if output_mode == "single":
            # Create single PDF with all pages
            output_pdf = f"{output_base}.pdf"
            if create_single_pdf_from_pages(converted_pages, output_pdf):
                print(f"Single PDF created: {output_pdf}")
                success_count = len(converted_pages)
            else:
                print("Failed to create single PDF")

        elif output_mode == "separate":
            # Create separate PDF for each page
            for page_num, converted_text in enumerate(converted_pages):
                output_pdf = f"{output_base}_page_{page_num + 1:03d}.pdf"
                if create_pdf_from_text(converted_text, output_pdf, page_num + 1):
                    print(f"    Saved page {page_num + 1} to {output_pdf}")
                    success_count += 1
                else:
                    print(f"    Failed to save page {page_num + 1}")

        elif output_mode == "text":
            # Create text files
            for page_num, converted_text in enumerate(converted_pages):
                output_file = f"{output_base}_page_{page_num + 1:03d}.txt"
                with open(output_file, "w", encoding='utf-8') as f:
                    f.write(f"PAGE {page_num + 1}\n")
                    f.write("="*60 + "\n\n")
                    f.write(converted_text)
                print(f"    Saved page {page_num + 1} to {output_file}")
                success_count += 1

        if output_mode == "single":
            print(f"\nConversion completed! Created single PDF with {len(converted_pages)} pages.")
        else:
            print(f"\nConversion completed! Created {success_count} files out of {len(converted_pages)} pages.")

        return True

    except Exception as e:
        print(f"Error processing PDF: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description='Convert PDF with Preeti text to Unicode PDF')
    parser.add_argument('inputFile', help='Input PDF file with Preeti text')
    parser.add_argument('outputBase', help='Output base name (without extension)')
    parser.add_argument('--mode', choices=['single', 'separate', 'text'], default='single',
                       help='Output mode: single (one PDF), separate (PDF per page), text (text files)')
    parser.add_argument('--pages', help='Page range (e.g., 1-5 or 1,3,5)', default=None)
    args = parser.parse_args()

    if not os.path.exists(args.inputFile):
        print(f"Error: File {args.inputFile} doesn't exist!")
        return 1

    if not args.inputFile.lower().endswith('.pdf'):
        print(f"Error: Input file must be a PDF!")
        return 1

    try:
        success = convert_pdf_page_wise(args.inputFile, args.outputBase, args.mode)
        return 0 if success else 1
    except Exception as e:
        print(f"Error: {e}")
        return 1

if __name__ == "__main__":
    exit(main())
