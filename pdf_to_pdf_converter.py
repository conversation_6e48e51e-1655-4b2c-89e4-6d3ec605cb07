#!/usr/bin/env python3
# pdf_to_pdf_converter.py - Convert PDF to new PDF with Unicode text

import argparse
import os
import fitz  # PyMuPDF
from pathlib import Path

# Preeti to Unicode mapping tables (same as before)
unicodeatoz=["ब","द","अ","म","भ","ा","न","ज","ष्","व","प","ि","फ","ल","य","उ","त्र","च","क","त","ग","ख","ध","ह","थ","श"]
unicodeAtoZ=["ब्","ध","ऋ","म्","भ्","ँ","न्","ज्","क्ष्","व्","प्","ी","ः","ल्","इ","ए","त्त","च्","क्","त्","ग्","ख्","ध्","ह्","थ्","श्"]
unicode0to9=["ण्","ज्ञ","द्द","घ","द्ध","छ","ट","ठ","ड","ढ"]
symbolsDict={
    "~":"ञ्", "`":"ञ", "!":"१", "@":"२", "#":"३", "$":"४", "%":"५", "^":"६", "&":"७", "*":"८", "(":"९", ")":"०",
    "-":"(", "_":")", "+":"ं", "[":"ृ", "{":"र्", "]":"े", "}":"ै", "\\":"्", "|":"्र", ";":"स", ":":"स्",
    "'":"ु", "\"":"ू", ",":"," , "<":"?", ".":"।", ">":"श्र", "/":"र", "?":"रु", "=":".", "ˆ":"फ्",
    "Î":"ङ्ख", "å":"द्व", "÷":"/"
}

def is_preeti_font(font_name):
    """Check if a font is Preeti or similar Nepali font."""
    if not font_name:
        return False
    
    font_name = font_name.lower()
    preeti_indicators = [
        'preeti', 'pcs', 'nepali', 'devanagari', 'kantipur', 'sagarmatha',
        'fontasy', 'kanchan', 'walkman', 'ananda', 'everest'
    ]
    
    return any(indicator in font_name for indicator in preeti_indicators)

def normalizePreeti(preetitxt):
    """Normalize Preeti text before conversion."""
    normalized = ''
    previoussymbol = ''
    preetitxt = preetitxt.replace('qm','s|')
    preetitxt = preetitxt.replace('f]','ो')
    preetitxt = preetitxt.replace('km','फ')
    preetitxt = preetitxt.replace('0f','ण')
    preetitxt = preetitxt.replace('If','क्ष')
    preetitxt = preetitxt.replace('if','ष')
    preetitxt = preetitxt.replace('cf','आ')
    
    index = -1
    while index + 1 < len(preetitxt):
        index += 1
        character = preetitxt[index]
        try:
            if preetitxt[index+2] == '{':
                if preetitxt[index+1] == 'f' or preetitxt[index+1] == 'ो':
                    normalized += '{' + character + preetitxt[index+1]
                    index += 2
                    continue
            if preetitxt[index+1] == '{':
                if character != 'f':
                    normalized += '{' + character
                    index += 1
                    continue
        except IndexError:
            pass
        
        if character == 'l':
            previoussymbol = 'l'
            continue
        else:
            normalized += character + previoussymbol
            previoussymbol = ''
    
    return normalized

def convert_preeti_to_unicode(preeti_text):
    """Convert Preeti text to Unicode."""
    converted = ''
    normalized_preeti = normalizePreeti(preeti_text)
    
    for character in normalized_preeti:
        try:
            if ord(character) >= 97 and ord(character) <= 122:  # a-z
                converted += unicodeatoz[ord(character)-97]
            elif ord(character) >= 65 and ord(character) <= 90:  # A-Z
                converted += unicodeAtoZ[ord(character)-65]
            elif ord(character) >= 48 and ord(character) <= 57:  # 0-9
                converted += unicode0to9[ord(character)-48]
            else:
                converted += symbolsDict.get(character, character)
        except (KeyError, IndexError):
            converted += character
    
    return converted

def convert_pdf_to_pdf(input_pdf, output_pdf):
    """Convert PDF with Preeti text to new PDF with Unicode text."""
    try:
        print(f"Processing PDF: {input_pdf}")
        input_doc = fitz.open(input_pdf)
        print(f"PDF has {len(input_doc)} pages")
        
        # Create new PDF document
        output_doc = fitz.open()
        
        for page_num in range(len(input_doc)):
            print(f"  Processing page {page_num + 1}...")
            
            # Get original page
            original_page = input_doc.load_page(page_num)
            page_rect = original_page.rect
            
            # Create new page with same dimensions
            new_page = output_doc.new_page(width=page_rect.width, height=page_rect.height)
            
            # Get text blocks with font and position information
            blocks = original_page.get_text("dict")
            
            for block in blocks["blocks"]:
                if "lines" in block:
                    for line in block["lines"]:
                        for span in line["spans"]:
                            text = span["text"]
                            font = span.get("font", "")
                            size = span.get("size", 12)
                            bbox = span.get("bbox", [0, 0, 0, 0])
                            
                            if text.strip():  # Only process non-empty text
                                if is_preeti_font(font):
                                    # Convert Preeti text
                                    converted_text = convert_preeti_to_unicode(text)
                                    # Use a Unicode-compatible font
                                    font_name = "helv"  # Helvetica supports Unicode
                                else:
                                    # Keep non-Preeti text as is
                                    converted_text = text
                                    font_name = "helv"
                                
                                # Insert text at original position
                                try:
                                    new_page.insert_text(
                                        (bbox[0], bbox[1] + size),  # Position
                                        converted_text,
                                        fontsize=size,
                                        fontname=font_name
                                    )
                                except Exception as e:
                                    print(f"    Warning: Could not insert text '{text[:20]}...': {e}")
        
        # Save the new PDF
        output_doc.save(output_pdf)
        output_doc.close()
        input_doc.close()
        
        print(f"Converted PDF saved as: {output_pdf}")
        return True
        
    except Exception as e:
        print(f"Error converting PDF: {e}")
        return False

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description='Convert PDF with Preeti text to new PDF with Unicode text')
    parser.add_argument('inputFile', help='Input PDF file with Preeti text')
    parser.add_argument('outputFile', help='Output PDF file with Unicode text')
    args = parser.parse_args()

    if not os.path.exists(args.inputFile) or not os.path.isfile(args.inputFile):
        print(f"Error: File {args.inputFile} doesn't exist!")
        return 1

    if not args.inputFile.lower().endswith('.pdf'):
        print(f"Error: Input file must be a PDF!")
        return 1

    if os.path.exists(args.outputFile):
        response = input(f"File {args.outputFile} already exists. Overwrite? (y/n): ")
        if response.lower() != 'y':
            print("Conversion cancelled.")
            return 1

    try:
        success = convert_pdf_to_pdf(args.inputFile, args.outputFile)
        return 0 if success else 1
        
    except Exception as e:
        print(f"Error during conversion: {e}")
        return 1

if __name__ == "__main__":
    exit(main())
